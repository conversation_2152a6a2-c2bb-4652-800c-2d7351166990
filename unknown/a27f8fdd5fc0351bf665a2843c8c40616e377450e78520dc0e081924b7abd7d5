package com.raindrop.identity_service.entity;

import com.raindrop.identity_service.enums.AuthProvider;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EntityListeners(AuditingEntityListener.class)
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    String id;
    @Column(unique = true)
    String username;
    String password;
    @Column(unique = true)
    String email;
    @Column(unique = true)
    String displayName;
    String avatarUrl;
    @Enumerated(EnumType.STRING)
    @Builder.Default
    AuthProvider authProvider = AuthProvider.LOCAL; // Mặc định là LOCAL
    @ManyToMany
    Set<Role> roles;
    @Column(updatable = false)
    @CreatedDate
    LocalDateTime createdAt;
    @LastModifiedDate
    LocalDateTime updatedAt;

    /**
     * Trạng thái tài khoản: true = đang hoạt động, false = bị khóa
     */
    @Builder.Default
    boolean enabled = true;
}
