package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.request.GenreRequest;
import com.raindrop.manga_service.dto.response.GenreResponse;
import com.raindrop.manga_service.entity.Genre;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-26T23:56:23+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Oracle Corporation)"
)
@Component
public class GenreMapperImpl implements GenreMapper {

    @Override
    public Genre toGenre(GenreRequest request) {
        if ( request == null ) {
            return null;
        }

        Genre.GenreBuilder genre = Genre.builder();

        genre.name( request.getName() );
        genre.description( request.getDescription() );

        return genre.build();
    }

    @Override
    public GenreResponse toGenreResponse(Genre genre) {
        if ( genre == null ) {
            return null;
        }

        GenreResponse.GenreResponseBuilder genreResponse = GenreResponse.builder();

        genreResponse.id( genre.getId() );
        genreResponse.name( genre.getName() );
        genreResponse.description( genre.getDescription() );

        return genreResponse.build();
    }

    @Override
    public void updateGenre(Genre genre, GenreRequest request) {
        if ( request == null ) {
            return;
        }

        genre.setName( request.getName() );
        genre.setDescription( request.getDescription() );
    }
}
