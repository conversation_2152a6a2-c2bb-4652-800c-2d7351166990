package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.dto.response.ReadingHistoryResponse;
import com.raindrop.manga_service.entity.Genre;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.mapper.MangaMapper;
import com.raindrop.manga_service.repository.ChapterRepository;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.httpclient.HistoryClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class RecommendationService {
    MangaRepository mangaRepository;
    ChapterRepository chapterRepository;
    HistoryClient historyClient;
    MangaMapper mangaMapper;

    public List<MangaSummaryResponse> getRecommendationsByGenreSummary(String userId, Integer limit) {
        int recommendationLimit = (limit != null && limit > 0) ? limit : 6;

        try {
            // 1. Lấy lịch sử đọc và extract genres
            List<String> targetGenres = getTargetGenresFromHistory(userId);
            if (targetGenres.isEmpty()) {
                return Collections.emptyList();
            }

            // 2. Lấy danh sách manga đã đọc để loại trừ
            List<String> excludeIds = getReadMangaIds(userId);

            // 3. Tìm manga gợi ý với scoring
            List<Manga> candidates = findCandidateMangas(targetGenres, excludeIds, recommendationLimit * 3);
            List<Manga> recommendedMangas = scoreAndSelectMangas(candidates, recommendationLimit);

            // 4. Convert to response với batch query cho lastChapterNumber
            return convertToResponseWithChapterInfo(recommendedMangas);

        } catch (Exception e) {
            log.error("Lỗi khi lấy gợi ý manga cho người dùng {}: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // Lấy target genres từ lịch sử đọc với trọng số
    private List<String> getTargetGenresFromHistory(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<ReadingHistoryResponse>> response = historyClient.getRecentReadingHistory(authHeader, userId, 5);

            if (response.getCode() != 200 || response.getResult().isEmpty()) {
                log.info("Không có lịch sử đọc cho user {}", userId);
                return Collections.emptyList();
            }

            List<String> recentMangaIds = response.getResult().stream()
                    .map(ReadingHistoryResponse::getMangaId)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("Recent manga IDs cho user {}: {}", userId, recentMangaIds);

            // Tính trọng số thể loại
            Map<String, Integer> genreCount = new HashMap<>();
            List<Manga> recentMangas = mangaRepository.findAllById(recentMangaIds);

            for (Manga manga : recentMangas) {
                for (Genre genre : manga.getGenres()) {
                    genreCount.put(genre.getName(), genreCount.getOrDefault(genre.getName(), 0) + 1);
                }
            }

            // Sắp xếp theo trọng số giảm dần
            List<String> sortedGenres = genreCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            log.info("Target genres cho user {} (theo trọng số): {}", userId, sortedGenres);
            return sortedGenres;
        } catch (Exception e) {
            log.error("Lỗi khi lấy target genres: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    // Lấy danh sách manga đã đọc để loại trừ
    private List<String> getReadMangaIds(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<String>> response = historyClient.getAllReadMangaIds(authHeader, userId);
            return (response.getCode() == 200 && response.getResult() != null) ?
                    response.getResult() : Collections.emptyList();
        } catch (Exception e) {
            log.error("Lỗi khi lấy read manga IDs: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest().getHeader("Authorization");
    }

    // Tìm manga candidates dựa trên genres
    private List<Manga> findCandidateMangas(List<String> targetGenres, List<String> excludeIds, int limit) {
        if (excludeIds.isEmpty()) {
            excludeIds = List.of("no-manga-id"); // Tránh lỗi SQL
        }
        return mangaRepository.findMangasByGenres(targetGenres, excludeIds, PageRequest.of(0, limit));
    }

    // Score và select manga với các yếu tố: recency, newness, random
    private List<Manga> scoreAndSelectMangas(List<Manga> candidates, int limit) {
        LocalDateTime now = LocalDateTime.now();

        return candidates.stream()
                .map(manga -> new ScoredManga(manga, calculateScore(manga, now)))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(limit)
                .map(sm -> sm.manga)
                .collect(Collectors.toList());
    }

    // Tính score cho manga: recency + newness + random
    private double calculateScore(Manga manga, LocalDateTime now) {
        double score = 1.0;

        // Recency boost: manga có chapter mới gần đây
        if (manga.getLastChapterAddedAt() != null) {
            long daysSinceLastChapter = ChronoUnit.DAYS.between(manga.getLastChapterAddedAt(), now);
            if (daysSinceLastChapter < 7) score *= 1.5;
            else if (daysSinceLastChapter < 30) score *= 1.2;
        }

        // Newness boost: manga được tạo gần đây
        if (manga.getCreatedAt() != null) {
            long daysSinceCreated = ChronoUnit.DAYS.between(manga.getCreatedAt(), now);
            if (daysSinceCreated < 30) score *= 1.3;
        }

        return score;
    }

    // Convert to response với batch query lastChapterNumber
    private List<MangaSummaryResponse> convertToResponseWithChapterInfo(List<Manga> mangas) {
        // Batch query tất cả lastChapterIds
        Set<String> chapterIds = mangas.stream()
                .map(Manga::getLastChapterId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<String, Double> chapterNumbers = chapterRepository.findAllById(chapterIds).stream()
                .collect(Collectors.toMap(
                    chapter -> chapter.getId(),
                    chapter -> chapter.getChapterNumber()
                ));

        return mangas.stream()
                .map(manga -> {
                    MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);
                    if (manga.getLastChapterId() != null) {
                        response.setLastChapterNumber(chapterNumbers.get(manga.getLastChapterId()));
                    }
                    return response;
                })
                .collect(Collectors.toList());
    }

    // Helper class cho scoring
    private static class ScoredManga {
        final Manga manga;
        final double score;

        ScoredManga(Manga manga, double score) {
            this.manga = manga;
            this.score = score;
        }
    }
}
