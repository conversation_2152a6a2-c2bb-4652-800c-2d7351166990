// Authentication Request structure
// { username: string, password: string }

// Authentication Response structure
// { token: string, refreshToken: string, authenticated: boolean, expiresIn?: number }

// Google Login Request structure
// { code: string, redirectUri: string }

// User Registration Request structure
// { username: string, password: string, email: string }

// User Response structure
// { id: string, username: string, email: string, roles: RoleResponse[], authProvider?: string }

// Role Response structure
// { name: string, description?: string, permissions?: PermissionResponse[] }

// Permission Response structure
// { name: string, description?: string }

// Refresh Token Request structure
// { refreshToken: string }

// Google Link Request structure
// { code: string, redirectUri: string }

// Link Local Account Request structure
// { username: string, email: string, password: string }

// Linked Account Response structure
// { id: string, provider: string, username?: string, email?: string, providerUserId?: string, linkedAt: string | Date }
